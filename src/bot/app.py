"""
EduGuideBot v3 Application
Main bot application setup and configuration.
"""

import logging
from telegram.ext import Application, CommandHandler, CallbackQueryHandler

from .commands_v3 import start_command, ai_status_command, ai_debug_command
from .handlers.assessment import handle_language_selection, handle_assessment_answer
from .handlers.recommendations import handle_recommendation_action, show_recommendations
from .handlers.details import show_major_details, show_university_info, handle_back_to_recommendations, contact_university, show_university_location
from .telegram_safe_v3 import log_telegram_errors

logger = logging.getLogger(__name__)


def create_bot_application(token: str) -> Application:
    """Create and configure EduGuideBot v3 application."""
    # Create application
    application = Application.builder().token(token).build()

    # Add command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("status", ai_status_command))
    application.add_handler(CommandHandler("debug", ai_debug_command))

    # Add callback query handlers with CORRECT patterns
    application.add_handler(CallbackQueryHandler(handle_language_selection, pattern="^lang_"))
    application.add_handler(CallbackQueryHandler(handle_assessment_answer, pattern="^ans_\\d+_\\d+$"))
    application.add_handler(CallbackQueryHandler(show_recommendations, pattern="^get_recommendations$"))
    application.add_handler(CallbackQueryHandler(show_recommendations, pattern="^QS_\\w+$"))
    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^major_\\d+$"))
    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^details_\\d+$"))
    application.add_handler(CallbackQueryHandler(contact_university, pattern="^contact_"))
    application.add_handler(CallbackQueryHandler(show_university_location, pattern="^location_"))
    application.add_handler(CallbackQueryHandler(show_university_info, pattern="^other_"))
    application.add_handler(CallbackQueryHandler(handle_back_to_recommendations, pattern="^back_to_recommendations$"))
    application.add_handler(CallbackQueryHandler(handle_recommendation_action, pattern="^back_to_menu$"))

    # Add error handler
    application.add_error_handler(log_telegram_errors)

    # Log handler count for debugging
    total_handlers = sum(len(handlers) for handlers in application.handlers.values())
    logger.info(f"EduGuideBot v3 application configured successfully")
    logger.info(f"Total handlers registered: {total_handlers}")

    # Count specific handler types
    command_count = 0
    callback_count = 0
    for group_handlers in application.handlers.values():
        for handler in group_handlers:
            if hasattr(handler, 'command'):
                command_count += 1
            elif hasattr(handler, 'pattern'):
                callback_count += 1

    logger.info(f"Command handlers: {command_count}")
    logger.info(f"Callback handlers: {callback_count}")

    return application



