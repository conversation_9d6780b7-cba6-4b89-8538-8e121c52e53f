"""
EduGuideBot v3 Details Handler
Handles detailed major and university information display
"""

import logging
import sys
from pathlib import Path
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.bot.telegram_safe_v3 import safe_answer_callback, safe_edit_message
from src.core.data_loader import load_raw

logger = logging.getLogger(__name__)


async def show_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show full program info when clicked."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        # Get selected major index from callback_data
        callback_parts = query.data.split("_")
        if len(callback_parts) < 2:
            await safe_edit_message(
                query,
                "❌ ទិន្នន័យមិនត្រឹមត្រូវ។ សូមព្យាយាមម្តងទៀត។"
            )
            return
        
        major_index = int(callback_parts[1])
        recommendations = context.user_data.get("recommendations", [])
        
        if major_index >= len(recommendations):
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីមុខជំនាញនេះ។"
            )
            return
        
        recommendation = recommendations[major_index]
        program = recommendation['program']
        
        # Format detailed information
        name_kh = program.get('major_name_kh', program.get('major_name_en', 'មិនមានឈ្មោះ'))
        uni_kh = program.get('university_name_kh', program.get('university_name_en', 'មិនមានឈ្មោះ'))
        
        detail_text = f"📌 ព័ត៌មានលម្អិតនៃជំនាញទី {major_index+1}:\n\n"
        detail_text += f"🎓 ជំនាញ: {name_kh}\n"
        detail_text += f"🏫 សាកលវិទ្យាល័យ: {uni_kh}\n"
        detail_text += f"📍 ទីតាំង: {program.get('city', 'មិនមានទីតាំង')}\n"
        
        # Duration and degree level
        duration = program.get('study_duration_kh', '')
        degree = program.get('degree_level_kh', '')
        if duration:
            detail_text += f"⏱️ រយៈពេល: {duration}\n"
        if degree:
            detail_text += f"🎓 កម្រិត: {degree}\n"
        
        # Fees
        fees_usd = program.get('tuition_fees_usd', '')
        fees_khr = program.get('tuition_fees_khr', '')
        if fees_usd:
            detail_text += f"💰 ថ្លៃសិក្សា: ${fees_usd}"
            if fees_khr:
                detail_text += f" / {fees_khr} រៀល"
            detail_text += " ក្នុងមួយឆ្នាំ\n"
        
        # Employment statistics
        employment_rate = program.get('employment_rate', '')
        avg_salary = program.get('average_starting_salary', '')
        if employment_rate:
            detail_text += f"📈 ឱកាសការងារ: {employment_rate}%\n"
        if avg_salary:
            detail_text += f"💵 ប្រាក់ខែដំបូង: {avg_salary}\n"
        
        # Faculty information
        faculty_kh = program.get('faculty_name_kh', '')
        if faculty_kh:
            detail_text += f"🏛️ មហាវិទ្យាល័យ: {faculty_kh}\n"
        
        # Language of instruction
        languages = program.get('language_of_instruction', [])
        if languages:
            detail_text += f"🗣️ ភាសាបង្រៀន: {', '.join(languages)}\n"
        
        # Career prospects
        careers_kh = program.get('potential_careers_kh', [])
        if careers_kh:
            detail_text += f"\n🎯 ការងារអនាគត:\n"
            for i, career in enumerate(careers_kh[:5], 1):  # Show top 5
                detail_text += f"{i}. {career}\n"
        
        # Academic requirements
        min_gpa = program.get('minimum_gpa', '')
        total_credits = program.get('total_credits', '')
        if min_gpa:
            detail_text += f"\n📚 GPA អប្បបរមា: {min_gpa}\n"
        if total_credits:
            detail_text += f"📖 ក្រេឌីតសរុប: {total_credits}\n"
        
        # Scholarship availability
        scholarship = program.get('scholarship_availability', False)
        if scholarship:
            detail_text += f"🎓 អាហារូបករណ៍: មាន\n"
        
        # Create action buttons
        keyboard_buttons = [
            [InlineKeyboardButton("🏫 មុខជំនាញផ្សេងទៀត", callback_data=f"other_majors_{program.get('university_id', '')}")],
            [InlineKeyboardButton("📍 ទីតាំងសាកលវិទ្យាល័យ", callback_data=f"uni_location_{program.get('university_id', '')}")],
            [InlineKeyboardButton("📞 ទំនាក់ទំនងសាកលវិទ្យាល័យ", callback_data=f"uni_contact_{program.get('university_id', '')}")],
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ]
        
        keyboard = InlineKeyboardMarkup(keyboard_buttons)
        await safe_edit_message(query, detail_text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_university_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show university information and other majors."""
    query = update.callback_query
    await safe_answer_callback(query)
    
    try:
        callback_parts = query.data.split("_")
        if len(callback_parts) < 3:
            await safe_edit_message(
                query,
                "❌ ទិន្នន័យមិនត្រឹមត្រូវ។ សូមព្យាយាមម្តងទៀត។"
            )
            return
        
        action_type = callback_parts[1]  # "majors", "location", "contact"
        university_id = callback_parts[2]
        
        # Load all programs to find university info
        all_programs = load_raw()
        university_programs = [p for p in all_programs if p.get('university_id') == university_id]
        
        if not university_programs:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return
        
        # Get university info from first program
        uni_info = university_programs[0]
        uni_name = uni_info.get('university_name_kh', uni_info.get('university_name_en', 'មិនមានឈ្មោះ'))
        
        if action_type == "majors":
            # Show other majors
            text = f"🏫 មុខជំនាញនៅ {uni_name}\n\n"
            
            for i, program in enumerate(university_programs[:10], 1):  # Show top 10
                major_name = program.get('major_name_kh', program.get('major_name_en', 'មិនមានឈ្មោះ'))
                fees = program.get('tuition_fees_usd', '')
                text += f"{i}. {major_name}\n"
                if fees:
                    text += f"   💰 ${fees} USD\n"
                text += "\n"
            
        elif action_type == "location":
            # Show location info
            text = f"📍 ទីតាំង {uni_name}\n\n"
            text += f"🏢 ទីតាំង: {uni_info.get('city', 'មិនមានទីតាំង')}\n"
            text += f"🌐 គេហទំព័រ: {uni_info.get('website', 'មិនមាន')}\n"
            
        elif action_type == "contact":
            # Show contact info
            text = f"📞 ទំនាក់ទំនង {uni_name}\n\n"
            text += f"🌐 គេហទំព័រ: {uni_info.get('website', 'មិនមាន')}\n"
            text += f"📧 សម្រាប់ព័ត៌មានបន្ថែម សូមចូលទៅកាន់គេហទំព័រ\n"
            
        else:
            text = "❌ សកម្មភាពមិនត្រឹមត្រូវ។"
        
        # Back button
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])
        
        await safe_edit_message(query, text, keyboard)
        
    except Exception as e:
        logger.error(f"Error in show_university_info: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def contact_university(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show university contact information."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        callback_parts = query.data.split("_")
        if len(callback_parts) < 2:
            await safe_edit_message(
                query,
                "❌ ទិន្នន័យមិនត្រឹមត្រូវ។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Get university info from callback data
        university_id = callback_parts[1] if len(callback_parts) > 1 else ""

        # Load all programs to find university info
        all_programs = load_raw()
        university_programs = [p for p in all_programs if str(p.get('university_id', '')) == str(university_id)]

        if not university_programs:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return

        # Get university info from first program
        uni_info = university_programs[0]
        uni_name = uni_info.get('university_name_kh', uni_info.get('university_name_en', 'មិនមានឈ្មោះ'))

        contact_text = f"📞 ទំនាក់ទំនង {uni_name}\n\n"
        contact_text += f"🌐 គេហទំព័រ: {uni_info.get('website', 'មិនមាន')}\n"
        contact_text += f"📧 អ៊ីមែល: {uni_info.get('email', 'មិនមាន')}\n"
        contact_text += f"📱 ទូរសព្ទ: {uni_info.get('phone', 'មិនមាន')}\n"
        contact_text += f"📍 អាសយដ្ឋាន: {uni_info.get('address', 'មិនមាន')}\n"

        # Back button
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])

        await safe_edit_message(query, contact_text, keyboard)

    except Exception as e:
        logger.error(f"Error in contact_university: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def show_university_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show university location information."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        callback_parts = query.data.split("_")
        if len(callback_parts) < 2:
            await safe_edit_message(
                query,
                "❌ ទិន្នន័យមិនត្រឹមត្រូវ។ សូមព្យាយាមម្តងទៀត។"
            )
            return

        # Get university info from callback data
        university_id = callback_parts[1] if len(callback_parts) > 1 else ""

        # Load all programs to find university info
        all_programs = load_raw()
        university_programs = [p for p in all_programs if str(p.get('university_id', '')) == str(university_id)]

        if not university_programs:
            await safe_edit_message(
                query,
                "❌ រកមិនឃើញព័ត៌មានអំពីសាកលវិទ្យាល័យនេះ។"
            )
            return

        # Get university info from first program
        uni_info = university_programs[0]
        uni_name = uni_info.get('university_name_kh', uni_info.get('university_name_en', 'មិនមានឈ្មោះ'))

        location_text = f"📍 ទីតាំង {uni_name}\n\n"
        location_text += f"🏢 ទីតាំង: {uni_info.get('city', 'មិនមានទីតាំង')}\n"
        location_text += f"📍 អាសយដ្ឋាន: {uni_info.get('address', 'មិនមាន')}\n"
        location_text += f"🗺️ ខេត្ត: {uni_info.get('province', 'មិនមាន')}\n"

        # Add map link if available
        if uni_info.get('city', '').lower() in ['phnom penh', 'ភ្នំពេញ']:
            location_text += f"🔗 ទីតាំងលើផែនទី: https://maps.google.com/?q=Phnom+Penh+Cambodia\n"
        elif uni_info.get('city', '').lower() in ['siem reap', 'សៀមរាប']:
            location_text += f"🔗 ទីតាំងលើផែនទី: https://maps.google.com/?q=Siem+Reap+Cambodia\n"

        # Back button
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")]
        ])

        await safe_edit_message(query, location_text, keyboard)

    except Exception as e:
        logger.error(f"Error in show_university_location: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )


async def handle_back_to_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle back to recommendations callback."""
    query = update.callback_query
    await safe_answer_callback(query)

    try:
        # Import here to avoid circular imports
        from .recommendations import show_recommendations
        await show_recommendations(update, context)

    except Exception as e:
        logger.error(f"Error in handle_back_to_recommendations: {e}")
        await safe_edit_message(
            query,
            "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញ។ សូមព្យាយាមម្តងទៀត។"
        )
